# This is just an example/base values file for the data center deployment.
# Already defined values may not be changed, empty values are required and have to be changed accordingly.
register-service:
  image:
    repository: ""
  imagePullSecrets:
    - name: ""

  ingress:
    enabled: true
    hosts:
      - host: ""
        paths:
          - path: /api
            pathType: Prefix

  externalDatabase:
    host: ""
    port: 5432
    user: ""
    password: ""
    databaseName: ""

  externalKeycloak:
    issuerUri: ""
    clientId: ""
    clientSecret: ""

  osInbox:
    tokenUri: "https://idp.serviceportal-stage.gemeinsamonline.de/webidp2/connect/token"
    clientId: "urn:digifischdok:stage"
    clientSecret: ""
    clientScope: "access_urn:dataport:od:digifischdok:stage:go:DigiFischDok,default"
    resource: "urn:dataport:osi:postfach:rz2:stage:go"
    baseUrn: "https://api-gateway-stage.dataport.de:443"

  externalMail:
    host: ""
    port: 25
    user: ""
    password: ""
    from: ""

  properties:
    SPRING_PROFILES_ACTIVE: default

  javaArgs:
    - "-Dhttp.proxyHost=${DATAPORT_PROXY_HOST}"
    - "-Dhttp.proxyPort=${DATAPORT_PROXY_PORT}"
    - "-Dhttps.proxyHost=${DATAPORT_PROXY_HOST}"
    - "-Dhttps.proxyPort=${DATAPORT_PROXY_PORT}"
    - "-Dhttp.nonProxyHosts=localhost|*.dsc.dataport.de|dstoragecloud.dataport.de"

  resources:
    requests:
      cpu: 1000m
      memory: 4Gi
    limits:
      cpu: 10000m
      memory: 4Gi

  networkPolicy:
    enabled: true
    podSelector:
      matchLabels:
        app.kubernetes.io/name: register-service
    ingress:
      - from:
          - namespaceSelector:
              matchLabels:
                kubernetes.io/metadata.name: kube-system
            podSelector:
              matchLabels:
                app.kubernetes.io/component: controller
                app.kubernetes.io/name: rke2-ingress-nginx
        ports:
          - port: 8080
            protocol: TCP
    egress:
      - ports:
          - port: 3128
            protocol: TCP
          - port: 5432
            protocol: TCP
          - port: 25
            protocol: TCP
          - port: 9021
            protocol: TCP
        to:
          - ipBlock:
              cidr: 10.0.0.0/8
      - ports:
          - port: 53
            protocol: UDP
        to:
          - namespaceSelector:
              matchLabels:
                kubernetes.io/metadata.name: kube-system
            podSelector:
              matchLabels:
                k8s-app: kube-dns

web-app:
  image:
    repository: ""
  imagePullSecrets:
    - name: ""

  ingress:
    enabled: true
    hosts:
      - host: ''
        paths:
          - path: /
            pathType: Prefix

  properties:
    API_URL: ""
    WEB_URL: ""
    KEYCLOAK_URL: ""
    KEYCLOAK_CLIENT_ID: ""
    KEYCLOAK_REALM: ""
    ENVIRONMENT: prod

  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 1000m
      memory: 128Mi

  networkPolicy:
    enabled: true
    defaultDenyEgress: true
    podSelector:
      matchLabels:
        app.kubernetes.io/name: web-app
    ingress:
      - from:
          - namespaceSelector:
              matchLabels:
                kubernetes.io/metadata.name: kube-system
            podSelector:
              matchLabels:
                app.kubernetes.io/component: controller
                app.kubernetes.io/name: rke2-ingress-nginx
        ports:
          # Use port 8080 because rootless nginx cannot run on port 80
          - port: 8080
            protocol: TCP