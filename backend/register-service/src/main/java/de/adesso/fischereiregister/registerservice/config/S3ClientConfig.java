package de.adesso.fischereiregister.registerservice.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Custom S3 client configuration to support specific S3 server requirements.
 * <p>
 * This configuration addresses two specific requirements:
 * 1. Trusts both system certificates and additional certificates from the certs folder
 * 2. Configures settings to work with S3 servers that require signature v2
 * <p>
 * This implementation uses AWS SDK v1 which has direct support for signature v2.
 */
@Configuration
@Slf4j
public class S3ClientConfig {

    @Value("${spring.cloud.aws.s3.endpoint}")
    private String endpoint;

    @Value("${spring.cloud.aws.s3.region}")
    private String region;

    @Value("${spring.cloud.aws.credentials.access-key}")
    private String accessKey;

    @Value("${spring.cloud.aws.credentials.secret-key}")
    private String secretKey;

    @Value("${spring.cloud.aws.s3.path-style-access-enabled:true}")
    private boolean pathStyleAccessEnabled;

    /**
     * Creates a custom trust manager that trusts both the system certificates
     * and additional certificates from the certs folder.
     */
    private X509TrustManager createCustomTrustManager() {
        try {
            TrustManagerFactory defaultTmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            defaultTmf.init((KeyStore) null); // null means use the default keystore

            X509TrustManager defaultTrustManager = null;
            for (TrustManager tm : defaultTmf.getTrustManagers()) {
                if (tm instanceof X509TrustManager) {
                    defaultTrustManager = (X509TrustManager) tm;
                    break;
                }
            }

            if (defaultTrustManager == null) {
                throw new IllegalStateException("Default X509TrustManager not found");
            }

            List<X509Certificate> customCertificates = loadCertificatesFromCertsFolder();

            final X509TrustManager finalDefaultTrustManager = defaultTrustManager;
            final List<X509Certificate> finalCustomCertificates = customCertificates;

            return new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    // Combine custom certificates with default accepted issuers
                    List<X509Certificate> allIssuers = new ArrayList<>(finalCustomCertificates);
                    allIssuers.addAll(Arrays.asList(finalDefaultTrustManager.getAcceptedIssuers()));
                    return allIssuers.toArray(new X509Certificate[0]);
                }

                @Override
                public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    finalDefaultTrustManager.checkClientTrusted(chain, authType);
                }

                @Override
                public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    if (!finalCustomCertificates.isEmpty()) {
                        boolean foundMatch = false;
                        for (X509Certificate cert : chain) {
                            for (X509Certificate customCert : finalCustomCertificates) {
                                if (cert.equals(customCert)) {
                                    foundMatch = true;
                                    break;
                                }
                            }
                            if (foundMatch) break;
                        }

                        if (foundMatch) {
                            return;
                        }
                    }

                    finalDefaultTrustManager.checkServerTrusted(chain, authType);
                }
            };
        } catch (Exception e) {
            throw new RuntimeException("Failed to create custom trust manager", e);
        }
    }

    /**
     * Loads certificates from the certs folder in the classpath.
     */
    private List<X509Certificate> loadCertificatesFromCertsFolder() {
        List<X509Certificate> certificates = new ArrayList<>();
        try {
            String certsPath = "certs";
            PathMatchingResourcePatternResolver resolver =
                    new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource[] resources = resolver.getResources("classpath:" + certsPath + "/*");

            if (resources.length == 0) {
                log.error("No files found in the certs directory");
                return certificates;
            }

            CertificateFactory cf = CertificateFactory.getInstance("X.509");

            for (org.springframework.core.io.Resource resource : resources) {
                String filename = resource.getFilename();
                if (filename == null) continue;

                if (filename.toLowerCase().endsWith(".crt") ||
                        filename.toLowerCase().endsWith(".cer") ||
                        filename.toLowerCase().endsWith(".pem")) {

                    X509Certificate cert = loadCertificateFromResource(cf, resource, filename);
                    if (cert != null) {
                        certificates.add(cert);
                    }
                }
            }

            if (certificates.isEmpty()) {
                log.error("No valid certificates were found in the certs folder");
            }
        } catch (Exception e) {
            log.error("Error scanning certificates from certs folder", e);
        }
        return certificates;
    }

    /**
     * Loads a certificate from a resource and adds it to the certificates list.
     *
     * @param cf       Certificate factory to use for parsing
     * @param resource Resource containing the certificate
     * @param filename Filename for logging purposes
     */
    private X509Certificate loadCertificateFromResource(
            CertificateFactory cf,
            org.springframework.core.io.Resource resource,
            String filename) {
        try (InputStream is = resource.getInputStream()) {
            X509Certificate cert = (X509Certificate) cf.generateCertificate(is);
            log.info("Loaded certificate: {} from file: {}", cert.getSubjectX500Principal().getName(), filename);
            return cert;
        } catch (Exception e) {
            log.error("Error loading certificate from {}", filename, e);
            return null;
        }
    }

    /**
     * Configures SSL context with our custom trust manager.
     */
    private SSLContext createSslContext() {
        try {
            X509TrustManager customTrustManager = createCustomTrustManager();
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{customTrustManager}, new SecureRandom());
            return sslContext;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create SSL context", e);
        }
    }

    /**
     * Creates a custom AmazonS3 client with specific configuration:
     * - Uses a custom trust manager that trusts both system certificates and certificates from the certs folder
     * - Uses signature version 2
     * - Configures path-style access if enabled
     *
     * @return Configured AmazonS3 client
     */
    @Bean
    public AmazonS3 amazonS3() {
        SSLContext sslContext = createSslContext();
        HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());

        AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);

        ClientConfiguration clientConfig = new ClientConfiguration()
                .withProtocol(Protocol.HTTPS)
                .withSignerOverride("S3SignerType") // Use signature v2
                .withUseExpectContinue(false); // Disable expect-continue for better compatibility

        clientConfig.setConnectionTimeout(10000);
        clientConfig.setSocketTimeout(10000);
        clientConfig.setMaxConnections(100);
        clientConfig.setMaxErrorRetry(3);

        SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
                sslContext,
                NoopHostnameVerifier.INSTANCE);

        clientConfig.getApacheHttpClientConfig().setSslSocketFactory(sslSocketFactory);

        return AmazonS3ClientBuilder.standard()
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint, region))
                .withPathStyleAccessEnabled(pathStyleAccessEnabled)
                .withClientConfiguration(clientConfig)
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .disableChunkedEncoding() // Disable chunked encoding for better compatibility
                .withForceGlobalBucketAccessEnabled(true) // Improve compatibility
                .build();
    }
}
