package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.view.bans_statistics.persistance.BansStatisticsView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BansStatisticsTransformationServiceImpl implements BansStatisticsTransformationService {

    @Override
    public List<BansStatistics> transformToBansStatistics(List<BansStatisticsView> statisticsViews) {
        try {
            // Group statistics by year
            Map<Integer, List<BansStatisticsView>> statsByYear = statisticsViews.stream()
                    .collect(Collectors.groupingBy(BansStatisticsView::getYear));

            List<BansStatistics> result = new ArrayList<>();

            // For each year, create a BansStatistics object
            for (Map.Entry<Integer, List<BansStatisticsView>> entry : statsByYear.entrySet()) {
                Integer year = entry.getKey();
                List<BansStatisticsView> yearStats = entry.getValue();

                // Sum issued and expired counts for the year
                int issuedCount = yearStats.stream()
                        .mapToInt(BansStatisticsView::getIssuedCount)
                        .sum();

                int expiredCount = yearStats.stream()
                        .mapToInt(BansStatisticsView::getExpiredCount)
                        .sum();

                int startedCount = yearStats.stream()
                        .mapToInt(BansStatisticsView::getStartedCount)
                        .sum();

                // Create data entry with issued, expired, and started counts
                BansStatisticsData data = new BansStatisticsData(issuedCount, startedCount, expiredCount);

                // Create BansStatistics with the year and data entry
                BansStatistics bansStatistics = new BansStatistics(year, data);
                result.add(bansStatistics);
            }

            // Sort the result by year in descending order
            result.sort(Comparator.comparingInt(BansStatistics::year).reversed());

            return result;
        } catch (Exception e) {
            log.error("Error transforming ban statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to transform ban statistics", e);
        }
    }
}
