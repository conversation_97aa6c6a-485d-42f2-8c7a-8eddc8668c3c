package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.view.bans_statistics.persistance.BansStatisticsView;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class BansStatisticsTransformationServiceImplTest {

    @InjectMocks
    private BansStatisticsTransformationServiceImpl transformationService;

    @Test
    @DisplayName("BansStatisticsTransformationServiceImpl.transformToBansStatistics should correctly group by year and sum issued and expired counts")
    void transformToBansStatistics_ShouldGroupByYearCorrectly() {
        // Given
        BansStatisticsView entry1 = createStatisticsView(2023, "BY", 5, 2);
        BansStatisticsView entry2 = createStatisticsView(2023, "BE", 3, 1);
        BansStatisticsView entry3 = createStatisticsView(2024, "BY", 8, 3);
        BansStatisticsView entry4 = createStatisticsView(2024, "BE", 4, 2);

        // When
        List<BansStatistics> result = transformationService.transformToBansStatistics(
                List.of(entry1, entry2, entry3, entry4));

        // Then
        assertThat(result).hasSize(2);

        // Check 2023 data
        BansStatistics stats2023 = result.stream()
                .filter(stat -> stat.year() == 2023)
                .findFirst()
                .orElseThrow();

        assertThat(stats2023.data().issued()).isEqualTo(8); // 5 + 3
        assertThat(stats2023.data().expired()).isEqualTo(3); // 2 + 1

        // Check 2024 data
        BansStatistics stats2024 = result.stream()
                .filter(stat -> stat.year() == 2024)
                .findFirst()
                .orElseThrow();

        assertThat(stats2024.data().issued()).isEqualTo(12); // 8 + 4
        assertThat(stats2024.data().expired()).isEqualTo(5); // 3 + 2
    }

    @Test
    @DisplayName("BansStatisticsTransformationServiceImpl.transformToBansStatistics should handle empty list")
    void transformToBansStatistics_ShouldHandleEmptyList() {
        // When
        List<BansStatistics> result = transformationService.transformToBansStatistics(List.of());

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("BansStatisticsTransformationServiceImpl.transformToBansStatistics should handle single entry")
    void transformToBansStatistics_ShouldHandleSingleEntry() {
        // Given
        BansStatisticsView entry = createStatisticsView(2023, "BY", 5, 2);

        // When
        List<BansStatistics> result = transformationService.transformToBansStatistics(List.of(entry));

        // Then
        assertThat(result).hasSize(1);
        assertThat(result.getFirst().year()).isEqualTo(2023);
        assertThat(result.getFirst().data().issued()).isEqualTo(5);
        assertThat(result.getFirst().data().expired()).isEqualTo(2);
    }

    private BansStatisticsView createStatisticsView(int year, String federalState, int issuedCount, int expiredCount) {
        BansStatisticsView view = new BansStatisticsView();
        view.setYear(year);
        view.setFederalState(federalState);
        view.setIssuedCount(issuedCount);
        view.setExpiredCount(expiredCount);
        return view;
    }
}
