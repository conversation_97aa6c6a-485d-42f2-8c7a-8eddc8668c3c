package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.model.Ban;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.consent.JurisdictionConsentInfo;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.view.bans_statistics.eventhandling.BansStatisticsViewEventHandler;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.UUID;

import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasSize;
import static org.mockito.Mockito.mock;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class BansStatisticsReadIntegrationTest {

    // greaterThanOrEqualTo is used for the assertions as the data is still contaminated by the file test-data.xml, please correct when the file gets deleted

    public static final String PARAM_YEAR = "year";
    public static final String PARAM_FEDERAL_STATE = "federalState";
    private static final String TEST_FEDERAL_STATE_SH = "SH";
    private static final String TEST_FEDERAL_STATE_HH = "HH";
    private static final int CURRENT_YEAR = LocalDate.now().getYear();
    private static final int PREVIOUS_YEAR = CURRENT_YEAR - 1;

    @Autowired
    private BansStatisticsViewEventHandler eventHandler;

    @Autowired
    private MockMvc mvc;

    @BeforeAll
    void setUp() {
        // Create events and call handler directly
        Instant currentYearInstant = LocalDateTime.of(CURRENT_YEAR, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC);

        BannedEvent banEventSH1 = createBanEvent(TEST_FEDERAL_STATE_SH, CURRENT_YEAR);
        BannedEvent banEventSH2 = createBanEvent(TEST_FEDERAL_STATE_SH, CURRENT_YEAR);
        BannedEvent banEventHH = createBanEvent(TEST_FEDERAL_STATE_HH, CURRENT_YEAR);

        UnbannedEvent unbanEventSH = createUnbanEvent(TEST_FEDERAL_STATE_SH);
        UnbannedEvent unbanEventHH = createUnbanEvent(TEST_FEDERAL_STATE_HH);

        BannedEvent banEventPreviousYearHH = createBanEvent(TEST_FEDERAL_STATE_HH, PREVIOUS_YEAR);

        eventHandler.on(banEventSH1);
        eventHandler.on(banEventSH2);
        eventHandler.on(banEventHH);
        eventHandler.on(unbanEventSH, currentYearInstant);
        eventHandler.on(unbanEventHH, currentYearInstant);
        eventHandler.on(banEventPreviousYearHH);
    }


    private BannedEvent createBanEvent(String federalState, int year) {
        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(federalState);

        // Create a ban with specific year for at and from dates
        Ban ban = new Ban();
        ban.setBanId(UUID.randomUUID());
        ban.setFileNumber("TEST-BAN-" + System.currentTimeMillis());
        ban.setReportedBy("Test Office");
        ban.setAt(LocalDate.of(year, 1, 10)); // Set the issue date in the specified year
        ban.setFrom(LocalDate.of(year, 1, 15)); // Set from date in the specified year
        ban.setTo(null); // Permanent ban

        // Create BannedEvent with the created ban
        return new BannedEvent(UUID.randomUUID(), ban, jurisdiction);
    }


    private UnbannedEvent createUnbanEvent(String federalState) {
        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(federalState);

        // Create UnbannedEvent
        return new UnbannedEvent(UUID.randomUUID(), jurisdiction);
    }


    private JurisdictionMovedEvent createJurisdictionMovedEvent(String fromFederalState, String toFederalState, Ban ban) {
        // Create jurisdictions for move
        Jurisdiction previousJurisdiction = new Jurisdiction();
        previousJurisdiction.setFederalState(fromFederalState);

        Jurisdiction newJurisdiction = new Jurisdiction();
        newJurisdiction.setFederalState(toFederalState);

        // Create JurisdictionMovedEvent
        return new JurisdictionMovedEvent(
                UUID.randomUUID(),
                previousJurisdiction,
                newJurisdiction,
                mock(JurisdictionConsentInfo.class),
                ban,
                new ArrayList<>(),
                "salt",
                "Test Office",
                new ArrayList<>(),
                SubmissionType.ANALOG
        );
    }

    @Test
    @DisplayName("""
            GET /api/statistics/bans
            Verify that the bans statistics endpoint can be reached and delivers the proper information.
            """)
    void callGetBansStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Verify year matches parameter
                .andExpect(jsonPath("$[0].data").exists()) // Verify data object exists
                .andExpect(jsonPath("$[0].data.issued").value(greaterThanOrEqualTo(2))) // Issued bans for current year (2 SH)
                .andExpect(jsonPath("$[0].data.expired").value(greaterThanOrEqualTo(1))) // Expired bans for current year (1 SH)
                .andExpect(jsonPath("$[0].data.started").value(greaterThanOrEqualTo(2))); // Started bans for current year (2 SH)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/bans with federal state parameter
            Verify that the bans statistics endpoint correctly filters by federal state.
            """)
    void callGetBansStatisticsWithFederalStateFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_HH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Verify year matches parameter
                .andExpect(jsonPath("$[0].data.issued").value(greaterThanOrEqualTo(1))) // Issued bans for current year (1 HH)
                .andExpect(jsonPath("$[0].data.expired").value(greaterThanOrEqualTo(0))) // Expired bans for current year (0 HH)
                .andExpect(jsonPath("$[0].data.started").value(greaterThanOrEqualTo(1))); // Started bans for current year (1 HH)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/bans with year parameter
            Verify that the bans statistics endpoint correctly filters by year.
            """)
    void callGetBansStatisticsWithYearFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans")
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_HH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(PREVIOUS_YEAR)) // Verify year matches parameter
                .andExpect(jsonPath("$[0].data.issued").value(greaterThanOrEqualTo(1))) // Issued bans for previous year (1 SH)
                .andExpect(jsonPath("$[0].data.expired").value(greaterThanOrEqualTo(0))) // Expired bans for previous year (0 SH)
                .andExpect(jsonPath("$[0].data.started").value(greaterThanOrEqualTo(1))); // Started bans for previous year (1 SH)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/bans with multiple year parameters
            Verify that the bans statistics endpoint correctly handles multiple year parameters.
            """)
    void callGetBansStatisticsWithMultipleYears() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_HH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(2))) // Years of data (2)
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // First result year (previous year)
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR)) // Second result year (current year)
                .andExpect(jsonPath("$[0].data.started").exists()) // Started bans for previous year
                .andExpect(jsonPath("$[1].data.started").exists()); // Started bans for current year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/bans without federalState parameter
            Verify that the bans statistics endpoint returns data for all federal states when no federalState parameter is provided.
            """)
    void callGetBansStatisticsWithoutFederalStateSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all federal states for the current year
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data.issued").value(greaterThanOrEqualTo(3))) // Issued bans for current year (2 SH + 1 HH)
                .andExpect(jsonPath("$[0].data.expired").value(greaterThanOrEqualTo(2))) // Expired bans for current year (1 SH + 1 HH)
                .andExpect(jsonPath("$[0].data.started").value(greaterThanOrEqualTo(3))); // Started bans for current year (2 SH + 1 HH)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/bans without parameters
            Verify that the bans statistics endpoint returns all available data when no parameters are provided.
            """)
    void callGetBansStatisticsWithoutParametersSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all years and federal states
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // First result year (current year)
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR)) // Second result year (previous  year)
                .andExpect(jsonPath("$[*].data").exists()) // Data objects exist
                .andExpect(jsonPath("$[0].data.issued").value(greaterThanOrEqualTo(3))) // Issued bans for current year (2 SH + 1 HH)
                .andExpect(jsonPath("$[0].data.expired").value(greaterThanOrEqualTo(2))) // Expired bans for current year (1 SH + 1 HH)
                .andExpect(jsonPath("$[0].data.started").value(greaterThanOrEqualTo(3))) // Started bans for current year (2 SH + 1 HH)
                .andExpect(jsonPath("$[1].data.issued").value(greaterThanOrEqualTo(1))) // Issued bans for previous year (1 SH)
                .andExpect(jsonPath("$[1].data.expired").value(greaterThanOrEqualTo(0))) // Expired bans for previous year (0 SH)
                .andExpect(jsonPath("$[1].data.started").value(greaterThanOrEqualTo(1))); // Started bans for previous year (1 SH)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/bans with non-existent year parameter
            Verify that the bans statistics endpoint returns an empty array for a non-existent year.
            """)
    void callGetBansStatisticsWithNonExistentYear() throws Exception {
        String nonExistentYear = String.valueOf(2000); // Use a year with no data

        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans")
                        .param(PARAM_YEAR, nonExistentYear)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray()) // Response is an array
                .andExpect(jsonPath("$").isEmpty()); // Empty array for non-existent year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/bans for jurisdiction move
            Verify that the jurisdiction move event correctly updates statistics.
            """)
    void callGetBansStatisticsForJurisdictionMove() throws Exception {
        // completely different federal states for this test
        final String SOURCE_STATE = "NW";
        final String TARGET_STATE = "BW";
        final int TEST_YEAR = 2021;

        // Set up initial statistics - create a ban in the source state
        BannedEvent initialBanEvent = createBanEvent(SOURCE_STATE, TEST_YEAR);
        eventHandler.on(initialBanEvent);

        // Verify initial state - source state should have 1 ban, target state should have 0
        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans")
                        .param(PARAM_YEAR, String.valueOf(TEST_YEAR))
                        .param(PARAM_FEDERAL_STATE, SOURCE_STATE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(TEST_YEAR))
                .andExpect(jsonPath("$[0].data.issued").value(1)); // Should have 1 ban in source state

        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans")
                        .param(PARAM_YEAR, String.valueOf(TEST_YEAR))
                        .param(PARAM_FEDERAL_STATE, TARGET_STATE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isEmpty()); // Target state should have no bans initially

        // Now move the jurisdiction
        JurisdictionMovedEvent moveEvent = createJurisdictionMovedEvent(SOURCE_STATE, TARGET_STATE, initialBanEvent.ban());
        eventHandler.on(moveEvent);

        // Verify after move - source state should have 0 bans, target state should have 1
        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans")
                        .param(PARAM_YEAR, String.valueOf(TEST_YEAR))
                        .param(PARAM_FEDERAL_STATE, SOURCE_STATE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(TEST_YEAR))
                .andExpect(jsonPath("$[0].data.issued").value(0)) // should be 0
                .andExpect(jsonPath("$[0].data.started").value(0));

        mvc.perform(MockMvcRequestBuilders.get("/statistics/bans")
                        .param(PARAM_YEAR, String.valueOf(TEST_YEAR))
                        .param(PARAM_FEDERAL_STATE, TARGET_STATE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(TEST_YEAR))
                .andExpect(jsonPath("$[0].data.issued").value(1)) // Target state should now have 1 ban
                .andExpect(jsonPath("$[0].data.started").value(1)); // Target state should now have 1 started ban
    }
}
