package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.Jurisdiction;
import de.adesso.fischereiregister.core.model.PaymentInfo;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.testutils.DomainTestData;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import de.adesso.fischereiregister.view.taxes_statistics.eventhandling.TaxesStatisticsViewEventHandler;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class TaxesStatisticsReadIntegrationTest {

    // greaterThanOrEqualTo is used for the assertions as the data is still contaminated by the file test-data.xml, please correct when the file gets deleted

    public static final String PARAM_YEAR = "year";
    public static final String PARAM_FEDERAL_STATE = "federalState";
    public static final String PARAM_OFFICE = "office";
    private static final String TEST_FEDERAL_STATE_SH = "SH";
    private static final String TEST_FEDERAL_STATE_HH = "HH";
    private static final String TEST_OFFICE = "Test Office";
    private static final int CURRENT_YEAR = LocalDate.now().getYear();
    private static final int PREVIOUS_YEAR = CURRENT_YEAR - 1;
    private static final double TAX_AMOUNT_SH = 31.0;
    private static final double TAX_AMOUNT_HH = 25.0;
    private static final double TAX_AMOUNT_VACATION = 15.0;
    private static final double TAX_AMOUNT_DIGITIZED = 40.0;

    @Autowired
    private TaxesStatisticsViewEventHandler eventHandler;

    @Autowired
    private MockMvc mvc;

    @BeforeAll
    void setUp() {
        // Create events and call handler directly
        Instant currentYearInstant = LocalDateTime.of(CURRENT_YEAR, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC);
        Instant previousYearInstant = LocalDateTime.of(PREVIOUS_YEAR, 1, 1, 0, 0, 0).toInstant(ZoneOffset.UTC);

        // Create FishingTaxPayedEvent for SH with ANALOG submission
        FishingTaxPayedEvent taxPayedEventSH1 = createFishingTaxPayedEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_SH, TAX_AMOUNT_SH);

        // Create FishingTaxPayedEvent for SH with ONLINE submission
        FishingTaxPayedEvent taxPayedEventSH2 = createFishingTaxPayedEvent(SubmissionType.ONLINE, TEST_FEDERAL_STATE_SH, TAX_AMOUNT_SH);

        // Create FishingTaxPayedEvent for HH
        FishingTaxPayedEvent taxPayedEventHH = createFishingTaxPayedEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_HH, TAX_AMOUNT_HH);

        // Create RegularLicenseCreatedEvent with taxes
        RegularLicenseCreatedEvent regularLicenseCreatedEventSH = createRegularLicenseEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_SH, TAX_AMOUNT_SH);

        // Create RegularLicenseDigitizedEvent with taxes
        RegularLicenseDigitizedEvent regularLicenseDigitizedEventSH = createRegularLicenseDigitizedEvent(TEST_FEDERAL_STATE_SH, TAX_AMOUNT_DIGITIZED);

        // Create VacationLicenseCreatedEvent with taxes
        VacationLicenseCreatedEvent vacationLicenseCreatedEventSH = createVacationLicenseEvent(SubmissionType.ONLINE, TEST_FEDERAL_STATE_SH, TAX_AMOUNT_VACATION);

        // Create FishingTaxPayedEvent for previous year
        FishingTaxPayedEvent taxPayedEventPreviousYear = createFishingTaxPayedEvent(SubmissionType.ANALOG, TEST_FEDERAL_STATE_SH, TAX_AMOUNT_SH);

        // Call event handler directly
        eventHandler.on(taxPayedEventSH1, currentYearInstant);
        eventHandler.on(taxPayedEventSH2, currentYearInstant);
        eventHandler.on(taxPayedEventHH, currentYearInstant);
        eventHandler.on(regularLicenseCreatedEventSH, currentYearInstant);
        eventHandler.on(regularLicenseDigitizedEventSH, currentYearInstant);
        eventHandler.on(vacationLicenseCreatedEventSH, currentYearInstant);
        eventHandler.on(taxPayedEventPreviousYear, previousYearInstant);
    }

    private FishingTaxPayedEvent createFishingTaxPayedEvent(SubmissionType submissionType, String federalState, double amount) {
        // Create a tax
        Tax tax = createTax(federalState, amount);

        // Create FishingTaxPayedEvent
        return new FishingTaxPayedEvent(
                UUID.randomUUID(),
                null, // consentInfo
                null, // person
                List.of(tax),
                "salt",
                List.of(), // identificationDocuments
                TEST_OFFICE, // issuedByOffice
                null, // inboxReference
                submissionType
        );
    }

    private RegularLicenseCreatedEvent createRegularLicenseEvent(SubmissionType submissionType, String federalState, double amount) {
        // Create a fishing license
        FishingLicense license = DomainTestData.createLicense();

        // Create a person
        Person person = DomainTestData.createPerson();

        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(federalState);

        // Create a tax
        Tax tax = createTax(federalState, amount);

        // Create RegularLicenseCreatedEvent
        return new RegularLicenseCreatedEvent(
                UUID.randomUUID(),
                "salt",
                null, // consentInfo
                person, // person
                List.of(), // fees
                List.of(tax), // taxes
                license,
                List.of(), // identificationDocuments
                jurisdiction,
                TEST_OFFICE, // issuedByOffice
                "Test Address", // issuedByAddress
                null, // inboxReference
                submissionType
        );
    }

    private RegularLicenseDigitizedEvent createRegularLicenseDigitizedEvent(String federalState, double amount) {
        // Create a fishing license
        FishingLicense license = DomainTestData.createLicense();
        license.setType(LicenseType.REGULAR);
        license.setIssuingFederalState(FederalState.valueOf(federalState));

        // Create a person
        Person person = DomainTestData.createPerson();

        // Create jurisdiction
        Jurisdiction jurisdiction = new Jurisdiction();
        jurisdiction.setFederalState(federalState);

        // Create a tax
        Tax tax = createTax(federalState, amount);

        // Create RegularLicenseDigitizedEvent
        return new RegularLicenseDigitizedEvent(
                UUID.randomUUID(),
                "salt",
                person,
                jurisdiction,
                license,
                List.of(), // fees
                List.of(tax), // taxes
                List.of(), // qualificationsProofs
                List.of(), // identificationDocuments
                null, // consentInfo
                TEST_OFFICE, // issuedByOffice
                "Test Address" // issuedByAddress
        );
    }

    private VacationLicenseCreatedEvent createVacationLicenseEvent(SubmissionType submissionType, String federalState, double amount) {
        // Create a fishing license
        FishingLicense license = DomainTestData.createLicense();
        license.setType(LicenseType.VACATION);
        license.setIssuingFederalState(FederalState.valueOf(federalState));

        // Create a tax
        Tax tax = createTax(federalState, amount);

        // Create VacationLicenseCreatedEvent
        return new VacationLicenseCreatedEvent(
                UUID.randomUUID(),
                null, // person
                "salt",
                null, // consentInfo
                List.of(), // fees
                List.of(tax), // taxes
                List.of(), // identificationDocuments
                license,
                TEST_OFFICE, // issuedByOffice
                null, // inboxReference
                submissionType
        );
    }

    private Tax createTax(String federalState, double amount) {
        Tax tax = new Tax();
        tax.setTaxId(UUID.randomUUID().toString());
        tax.setFederalState(federalState);
        tax.setValidFrom(LocalDate.of(LocalDate.now().getYear(), 1, 1));
        tax.setValidTo(LocalDate.of(LocalDate.now().getYear(), 12, 31));

        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setAmount(amount);
        paymentInfo.setType(PaymentType.ONLINE);

        tax.setPaymentInfo(paymentInfo);

        return tax;
    }

    @Test
    @DisplayName("""
            GET /api/statistics/taxes
            Verify that the taxes statistics endpoint can be reached and delivers the proper information.
            """)
    void callGetTaxesStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/taxes")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data", hasSize(greaterThanOrEqualTo(2)))) // At least 2 submission types
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(2)))) // Taxes with ANALOG submission (2 SH)
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(2)))) // Taxes with ONLINE submission (2 SH)
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH + TAX_AMOUNT_DIGITIZED)))) // Revenue for ANALOG submissions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH + TAX_AMOUNT_VACATION)))); // Revenue for ONLINE submissions
    }

    @Test
    @DisplayName("""
            GET /api/statistics/taxes with federal state parameter
            Verify that the taxes statistics endpoint correctly filters by federal state.
            """)
    void callGetTaxesStatisticsWithFederalStateFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/taxes")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_HH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(1)))) // Count for ANALOG submissions in HH
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_HH))));// Revenue for ANALOG submissions in HH
    }

    @Test
    @DisplayName("""
            GET /api/statistics/taxes with year parameter
            Verify that the taxes statistics endpoint correctly filters by year.
            """)
    void callGetTaxesStatisticsWithYearFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/taxes")
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(PREVIOUS_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(1)))) // Count for ANALOG submissions in previous year
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH)))); // Revenue for ANALOG submissions in previous year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/taxes with multiple year parameters
            Verify that the taxes statistics endpoint correctly handles multiple year parameters.
            """)
    void callGetTaxesStatisticsWithMultipleYears() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/taxes")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(2))) // Years of data (2)
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // First result year (current year)
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR)) // Second result year (previous year)
                // Previous year assertions
                .andExpect(jsonPath("$[1].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(1)))) // Count for ANALOG submissions in previous year
                .andExpect(jsonPath("$[1].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH)))) // Revenue for ANALOG submissions in previous year
                // Current year assertions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(3)))) // Count for ANALOG submissions in current year
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(2)))) // Count for ONLINE submissions in current year
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH + TAX_AMOUNT_DIGITIZED + TAX_AMOUNT_HH)))) // Revenue for ANALOG submissions in current year
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH + TAX_AMOUNT_VACATION)))); // Revenue for ONLINE submissions in current year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/taxes without federalState parameter
            Verify that the taxes statistics endpoint returns data for all federal states when no federalState parameter is provided.
            """)
    void callGetTaxesStatisticsWithoutFederalStateSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/taxes")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all federal states for the current year
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(3)))) // Count for ANALOG submissions (2 SH + 1 HH)
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(2)))) // Count for ONLINE submissions (2 SH)
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH + TAX_AMOUNT_DIGITIZED + TAX_AMOUNT_HH)))) // Revenue for ANALOG submissions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH + TAX_AMOUNT_VACATION)))); // Revenue for ONLINE submissions
    }

    @Test
    @DisplayName("""
            GET /api/statistics/taxes with office parameter
            Verify that the taxes statistics endpoint correctly filters by office.
            """)
    void callGetTaxesStatisticsWithOfficeSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/taxes")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_OFFICE, TEST_OFFICE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").isArray()) // Data is an array
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(1)))) // Count for ANALOG submissions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(1)))) // Count for ONLINE submissions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH + TAX_AMOUNT_DIGITIZED)))) // Revenue for ANALOG submissions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH + TAX_AMOUNT_VACATION)))); // Revenue for ONLINE submissions
    }

    @Test
    @DisplayName("""
            GET /api/statistics/taxes without parameters
            Verify that the taxes statistics endpoint returns all available data when no parameters are provided.
            """)
    void callGetTaxesStatisticsWithoutParametersSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/taxes")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all years and federal states
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // First result year (current year)
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR)) // Second result year (previous year)
                .andExpect(jsonPath("$[*].data").exists()) // Data objects exist
                // Current year assertions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(3)))) // Count for ANALOG submissions in current year
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].count").value(hasItem(greaterThanOrEqualTo(2)))) // Count for ONLINE submissions in current year
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH + TAX_AMOUNT_DIGITIZED + TAX_AMOUNT_HH)))) // Revenue for ANALOG submissions in current year
                // Previous year assertions
                .andExpect(jsonPath("$[0].data[?(@.submissionType == 'ONLINE')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH + TAX_AMOUNT_VACATION)))) // Revenue for ONLINE submissions in current year
                .andExpect(jsonPath("$[1].data[?(@.submissionType == 'ANALOG')].count").value(hasItem(greaterThanOrEqualTo(1)))) // Count for ANALOG submissions in previous year
                .andExpect(jsonPath("$[1].data[?(@.submissionType == 'ANALOG')].revenue").value(hasItem(greaterThanOrEqualTo(TAX_AMOUNT_SH)))); // Revenue for ANALOG submissions in previous year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/taxes with non-existent year parameter
            Verify that the taxes statistics endpoint returns an empty array for a non-existent year.
            """)
    void callGetTaxesStatisticsWithNonExistentYear() throws Exception {
        String nonExistentYear = String.valueOf(2000); // Use a year with no data

        mvc.perform(MockMvcRequestBuilders.get("/statistics/taxes")
                        .param(PARAM_YEAR, nonExistentYear)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray()) // Response is an array
                .andExpect(jsonPath("$").isEmpty()); // Empty array for non-existent year
    }
}
