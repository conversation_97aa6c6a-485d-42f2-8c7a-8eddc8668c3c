import { defineConfig } from 'cypress';
import * as os from 'os';
import { readFileSync, writeFileSync, mkdirSync, existsSync } from 'fs';
import path, { join } from 'path';

export default defineConfig({
  chromeWebSecurity: false,
  projectId: 'FISH',

  e2e: {
    baseUrl:'http://localhost',
    testIsolation: false,
    setupNodeEvents(on, config) {
      const testDataPath = path.resolve(__dirname, './cypress/fixtures/dev/suche/testdata.json');
      const rawData = readFileSync(testDataPath, 'utf-8');
      const testData = JSON.parse(rawData);
      const envName = config.env.envName || process.env.CYPRESS_envName;
      const baseUrl = testData[`${envName}`];
      if (!baseUrl) {          
        throw new Error(`Unknown envName '${envName}'. Available keys: ${Object.keys(testData).join(', ')}`);
      }
      config.baseUrl = baseUrl;
     let allureCypress: any;
      on('task', {
        initializeAllureReporter: async () => {
          try {
            const allureLib = await import('allure-cypress');
            allureCypress = allureLib?.default;
            if (typeof allureCypress === 'function') {
              allureCypress(on, config, {
                resultsDir: `cypress/results/allure-results/${envName}`,
                environmentInfo: {
                  os_platform: os.platform(),
                  os_release: os.release(),
                  os_version: os.version ? os.version() : 'Version unavailable',
                  node_version: process.version,
                },
              });
              console.log('Allure reporter initialized via task.');
            } else {
              console.warn('Error initializing allure-cypress via task: allureCypress is not a function');
            }
          } catch (error) {
            console.warn('allure-cypress not found during dynamic import in task:', error);
          }
          return null;
        },
        table(message) {
          console.table(message);
          const allureResultsDir = `cypress/results/allure-results/${envName}`;
          const errorLogPath = join(allureResultsDir, 'accessibilityErrors.log');
          if (!existsSync(allureResultsDir)) {
            mkdirSync(allureResultsDir, { recursive: true });
          }
          const messageString = JSON.stringify(message, null, 2);
          writeFileSync(errorLogPath, `${messageString}\n`, { flag: 'a' });
          return null;
        },
        allureEnvironmentInfo: (envInfo) => {
          const allureResultsDir = `cypress/results/allure-results/${envName}`;
          const filePath = join(allureResultsDir, 'environment.properties');
          const content = Object.entries(envInfo).map(([key, value]) => `${key}=${value}`).join('\n');
          writeFileSync(filePath, content, { encoding: 'utf8' });
          return null;
        },
        reportAllureCypressSpecMessages: () => {
          return null;
        }
      });
      config.reporter = 'cypress-multi-reporters';
      config.reporterOptions = {
        reporterEnabled: 'mochawesome',
        mochawesomeReporterOptions: {
          reportDir: `cypress/results/mochawesome/${envName}`,
          overwrite: false,
          html: true,
          json: true,
          timestamp: 'yyyymmdd_HHMMss',
          consoleReporter: 'spec',
        },
      };
      return config;
    },
    defaultCommandTimeout: 10000,
    includeShadowDom: true,
    viewportWidth: 1280,
    viewportHeight: 1280,
    video: false,
    screenshotsFolder: 'cypress/results/screenshots',
    retries: {
      runMode: 1,
      openMode: 0,
    },
    env: {
      system: 'dev',
    },
  },
});

