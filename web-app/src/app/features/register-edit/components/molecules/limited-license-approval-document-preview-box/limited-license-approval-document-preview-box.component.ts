import { ChangeDetectionStrategy, Component } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { CardContentComponent } from '@/app/shared/atoms/card-content/card-content.component';
import { CardFooterComponent } from '@/app/shared/atoms/card-footer/card-footer.component';
import { CardHeaderComponent } from '@/app/shared/atoms/card-header/card-header.component';
import { PaymentItemMainAreaComponent } from '@/app/shared/atoms/payment-item-main-area/payment-item-main-area.component';
import { IconDocumentPdfComponent } from '@/app/shared/icons/document-pdf/document-pdf.component';
import { IconLinkComponent } from '@/app/shared/icons/link/link.component';
import { CardComponent } from '@/app/shared/molecules/card/card.component';
import { PaymentItemComponent } from '@/app/shared/molecules/payment-item/payment-item.component';

@Component({
  selector: 'fish-limited-license-approval-document-preview-box',
  standalone: true,
  imports: [
    CardComponent,
    CardHeaderComponent,
    ButtonComponent,
    IconLinkComponent,
    CardContentComponent,
    CardFooterComponent,
    IconDocumentPdfComponent,
    PaymentItemComponent,
    PaymentItemMainAreaComponent,
    TranslateModule,
  ],
  templateUrl: './limited-license-approval-document-preview-box.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LimitedLicenseApprovalDocumentPreviewBoxComponent {
  protected showDocumentButtonClicked(): void {
    console.warn('This feature will be implemented in a future merge request.');
  }
}
