import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit, computed, input } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';

import { TranslateService } from '@ngx-translate/core';
import { Observable, combineLatest, forkJoin, map, of, startWith } from 'rxjs';

import { AutoGeneratedIdComponent } from '@/app/shared/atoms/auto-generated-id-component/auto-generated-id.component';
import { InputComponent } from '@/app/shared/atoms/input/input.component';
import { LabelComponent } from '@/app/shared/atoms/label/label.component';
import { TextareaComponent } from '@/app/shared/atoms/textarea/textarea.component';
import { YearSkipperComponent } from '@/app/shared/atoms/year-skipper/year-skipper.component';
import { ComboboxComponent } from '@/app/shared/molecules/combobox/combobox.component';
import { ValidationErrorMapping } from '@/app/shared/organisms/form-field/form-field.models';

@Component({
  standalone: true,
  templateUrl: './form-field.component.html',
  selector: 'fish-form-field',
  imports: [CommonModule, LabelComponent, InputComponent, ReactiveFormsModule, ComboboxComponent, YearSkipperComponent, TextareaComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormFieldComponent extends AutoGeneratedIdComponent implements OnInit {
  /**
   * Fields to be forwarded to the Children Component [InputComponent|ComboboxComponent]
   */
  @Input() public size: 'large' | 'medium' = 'medium';

  @Input() public placeholder: string = '';

  @Input() public type: 'text' | 'number' | 'date' | 'combobox' | 'year-skipper' | 'textarea' = 'text';

  @Input() public maxLength: number | null = null;

  @Input() public minLength: number | null = null;

  @Input() public control = new FormControl();

  @Input() public options: string[] = [];

  @Input() public min!: number;

  @Input() public max!: number;

  @Input() public showOptionalLabel = true;

  @Input() public showErrorLabel = true;

  /**
   * May be set to specify whether a year-stepper should be used for durations instead of static years
   */
  @Input() public isDuration: boolean = false;

  /**
   * Mapping from error key to the displayed Message.
   */
  @Input() public errorMapping$?: Observable<ValidationErrorMapping>;

  /** Label to display over the Input field. */
  @Input() public label?: string;

  /** Additional text to further describe the input, displayed directly above the Input field. */
  @Input() public subLabel?: string;

  public readonly showCharacterCount = input<boolean>(false);

  protected readonly errorMessageId = `${this.id}-error`;

  protected errorText$: Observable<string | null> = of(null);

  protected defaultErrorMapping$: Observable<ValidationErrorMapping> = of({});

  protected readonly required = computed(() => {
    return this.control.hasValidator(Validators.required);
  });

  constructor(
    private readonly translate: TranslateService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  public ngOnInit(): void {
    if (this.minLength != null) {
      this.control.addValidators(Validators.minLength(this.minLength));
    }

    if (this.maxLength != null) {
      this.control.addValidators(Validators.maxLength(this.maxLength));
    }

    this.defaultErrorMapping$ = forkJoin([
      this.translate.get('common.form.error.required', { control: this.label }),
      this.translate.get('common.form.error.whitespace'),
      this.translate.get('common.form.error.pattern', { control: this.label }),
      this.translate.get('common.form.error.max_length', { control: this.label, length: this.maxLength }),
      this.translate.get('common.form.error.min_length', { control: this.label, length: this.minLength }),
      this.translate.get('common.form.error.combobox', { control: this.label }),
    ]).pipe(
      map(([errorRequired, errorWhitespace, errorPattern, errorMaxLength, errorMinLength, errorCombobox]) => ({
        required: errorRequired,
        whitespace: errorWhitespace,
        pattern: errorPattern,
        maxlength: errorMaxLength,
        minlength: errorMinLength,
        invalidComboboxOption: errorCombobox,
      }))
    );

    if (!this.errorMapping$) {
      this.errorMapping$ = of({});
    }

    this.errorText$ = combineLatest([this.control.statusChanges.pipe(startWith(null)), this.errorMapping$, this.defaultErrorMapping$]).pipe(
      map(([_, errors, defaults]) => {
        const control = this.control;

        if (!control?.errors) {
          return null;
        }

        const errorKeys = Object.keys(control?.errors);
        const firstError = errorKeys.at(0);
        if (!firstError) {
          return null;
        }

        if (errors[firstError]) {
          return errors[firstError];
        }

        return defaults[firstError] ?? 'Error';
      })
    );
    this.control.parent?.statusChanges.subscribe(() => {
      this.cdr.detectChanges();
    });
    this.control.statusChanges.subscribe(() => {
      this.cdr.detectChanges();
    });
  }
}
