<div class="flex w-full flex-col gap-1">
  <div class="flex justify-between">
    @if (label || subLabel) {
      <div class="flex flex-col">
        @if (label) {
          <fish-label
            [for]="id"
            [required]="required()"
            [showOptionalLabel]="showOptionalLabel"
            [disabled]="control.disabled"
            [invalid]="control.touched && control.invalid"
          >
            {{ label }}
          </fish-label>
        }
        @if (subLabel) {
          <span class="text-font-secondary" [innerText]="subLabel"></span>
        }
      </div>
    }
    @if (showCharacterCount()) {
      <span class="text-xs text-font-secondary">
        <span class="font-bold" [innerText]="control.value?.length ?? 0"></span>
        @if (maxLength) {
          <span [innerText]="' / ' + maxLength"></span>
        }
      </span>
    }
  </div>

  @if (type === 'combobox') {
    <fish-combobox
      [id]="id"
      [control]="control"
      [placeholder]="placeholder"
      [options]="options"
      [attr.aria-describedby]="control.touched && control.invalid ? errorMessageId : null"
    ></fish-combobox>
  } @else if (type === 'year-skipper') {
    <fish-year-skipper
      [id]="id"
      [label]="label ?? ''"
      [control]="control"
      [placeholder]="placeholder"
      [size]="size"
      [min]="min"
      [max]="max"
      [isDuration]="isDuration"
      [attr.aria-describedby]="control.touched && control.invalid ? errorMessageId : null"
    ></fish-year-skipper>
  } @else if (type === 'textarea') {
    <fish-textarea
      [id]="id"
      [control]="control"
      [placeholder]="placeholder"
      [attr.aria-describedby]="control.touched && control.invalid ? errorMessageId : null"
    ></fish-textarea>
  } @else {
    <fish-input
      [id]="id"
      [control]="control"
      [type]="type"
      [placeholder]="placeholder"
      [size]="size"
      [attr.aria-describedby]="control.touched && control.invalid ? errorMessageId : null"
    ></fish-input>
  }

  @if (showErrorLabel) {
    <div class="w-full pl-6 text-feedback-text-error">
      <span *ngIf="control.touched" [id]="errorMessageId" aria-live="polite">
        {{ errorText$ | async }}
      </span>
    </div>
  }
</div>
