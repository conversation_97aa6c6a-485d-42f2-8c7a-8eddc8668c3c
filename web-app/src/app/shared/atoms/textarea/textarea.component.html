<fish-focus-ring class="w-full">
  <div [class]="backgroundClasses()">
    <div
      [class]="textareaWrapperClasses()"
      class="peer flex min-w-0 items-center justify-between text-base outline-none backdrop-blur-xl transition-[background-color,border-color,color,fill,stroke,opacity,box-shadow,transform] duration-240 ease-out"
    >
      <textarea
        [id]="id()"
        (focus)="onFocus()"
        (input)="onInput($event)"
        (keydown.enter)="submitted.emit('')"
        [formControl]="control()"
        [placeholder]="placeholder()"
        [class]="textareaClasses()"
        [attr.aria-required]="required()"
        [attr.aria-invalid]="control().touched && control().invalid"
        class="w-full bg-transparent outline-none placeholder:font-thin placeholder:italic placeholder:transition-all placeholder:duration-240 placeholder:ease-out"
        fishTrim
        data-testid="input"
      ></textarea>
      @if (control().touched && control().invalid) {
        <fish-icon-warning class="text-feedback-text-warning" size="48" />
      }
      <ng-content />
    </div>
  </div>
  <div [class]="borderClasses()"></div>
</fish-focus-ring>
