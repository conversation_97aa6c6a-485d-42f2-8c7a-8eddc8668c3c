import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, input, output } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';

import { twMerge } from 'tailwind-merge';

import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';
import { backgroundVariants, borderVariants, textareaVariants, textareaWrapperVariants } from '@/app/shared/atoms/textarea/textarea.component.styles';
import { TrimDirective } from '@/app/shared/directives/TrimDirective';
import { IconWarningComponent } from '@/app/shared/icons/warning/warning.component';

@Component({
  selector: 'fish-textarea',
  standalone: true,
  imports: [FocusRingComponent, IconWarningComponent, ReactiveFormsModule, TrimDirective],
  templateUrl: './textarea.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TextareaComponent implements AfterViewInit {
  public readonly placeholder = input<string>('');

  public readonly class = input<string>('');

  public readonly control = input.required<FormControl>();

  /** The id is required to ensure accessibility */
  public readonly id = input.required<string>();

  public readonly textareaFocused = output<void>();

  public readonly textareaChanged = output<string>();

  public readonly submitted = output<string>();

  private readonly cdr: ChangeDetectorRef = inject(ChangeDetectorRef);

  protected readonly required = computed(() => {
    return this.control().hasValidator(Validators.required);
  });

  protected readonly borderClasses = computed<string>(() => {
    return twMerge(
      borderVariants({
        disabled: this.control().disabled,
      })
    );
  });

  protected readonly textareaWrapperClasses = computed<string>(() => {
    return twMerge(
      textareaWrapperVariants({
        disabled: this.control().disabled,
      })
    );
  });

  protected readonly textareaClasses = computed<string>(() => {
    return twMerge(
      textareaVariants({
        valid: !(this.control().touched && this.control().invalid),
      })
    );
  });

  protected readonly backgroundClasses = computed<string>(() => {
    return twMerge(
      backgroundVariants({
        disabled: this.control().disabled,
      })
    );
  });

  public ngAfterViewInit(): void {
    this.control().parent?.statusChanges.subscribe(() => this.cdr.detectChanges());
    this.control().statusChanges.subscribe(() => this.cdr.detectChanges());
  }

  protected onFocus(): void {
    this.textareaFocused.emit();
  }

  protected onInput(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.textareaChanged.emit(value);
  }
}
