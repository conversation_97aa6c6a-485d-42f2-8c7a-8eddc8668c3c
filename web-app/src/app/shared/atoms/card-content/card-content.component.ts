import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';

import { twMerge } from 'tailwind-merge';

import { cardContentBackgroundStyles } from '@/app/shared/atoms/card-content/card-content.component.styles';

@Component({
  selector: 'fish-card-content',
  standalone: true,
  imports: [],
  templateUrl: './card-content.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CardContentComponent {
  public readonly variant = input<'default' | 'pattern'>('default');

  protected readonly backgroundClasses = computed(() => {
    return twMerge(cardContentBackgroundStyles({ variant: this.variant() }));
  });
}
