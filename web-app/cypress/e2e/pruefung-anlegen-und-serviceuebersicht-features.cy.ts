import * as allure from "allure-js-commons";
import { ExternalLoginPagePo } from "@/support/pageObjects/external-login-page.po";
import { LoginPagePo } from "@/support/pageObjects/login-page.po";
import {DigitizeLicensePo} from "../support/pageObjects/digitize-licence/digitize-license-page.po";
import { ExaminationPagePo } from "@/support/pageObjects/examination-page.po";
import { ServiceOverviewPo } from "@/support/pageObjects/service-overview.po";
import { HomePagePo } from "@/support/pageObjects/home-page.po";
import { SearchResultsPo } from "@/support/pageObjects/search-results.po";
import { ConsentDigitizePo } from "@/support/pageObjects/consent-digitize-page.po";
import { HeaderComponentCo } from "@/support/sharedComponents/header-component.co";
import { EditFooterComponentCo } from "@/support/sharedComponents/edit-footer-component.co";
import { BanPersonPagePo } from '@/support/pageObjects/ban-person-page.po'

// Testsuite für Fischereischein Anlage
describe('Prüfung anlegen und Serviceübersicht Funktionalitäten testen', () => {
const digitizeLicencePage: DigitizeLicensePo = new DigitizeLicensePo();
const examinationPage: ExaminationPagePo = new ExaminationPagePo();
const loginPage: LoginPagePo = new LoginPagePo();
const externalLoginPage: ExternalLoginPagePo = new ExternalLoginPagePo();
const serviceOverviewPage: ServiceOverviewPo = new ServiceOverviewPo();
const homePage: HomePagePo = new HomePagePo();
const searchResultsPage: SearchResultsPo = new SearchResultsPo();
const consentPage: ConsentDigitizePo = new ConsentDigitizePo();
const headerComponentPage: HeaderComponentCo = new HeaderComponentCo();
const editFooterComponentPage: EditFooterComponentCo = new EditFooterComponentCo();
const banPersonPage: BanPersonPagePo = new BanPersonPagePo();

let testUsers: any;
let testData: any;
let testCitizen: any;

const currentDate = new Date().toISOString().split('T')[0];
const tomorrowDate = new Date();
tomorrowDate.setDate(tomorrowDate.getDate() + 1);
const system = Cypress.env('system');

before(() => {    
  cy.clearCookies();    
  allure.feature('Prüfung anlegen und Serviceübersicht Features');    
  allure.owner('Hegedus Kinga');    
  
  cy.fixture(`${system}/suche/testuser.json`).then((users) => {      
    cy.log('reading the test users file');      
    testUsers = users;      
    cy.log(`run Tests using test users on: ${system}`);    
  });    
  
  cy.fixture(`${system}/suche/testdata.json`).then((data) => {      
    cy.log('reading the test data file');      
    testData = data;      
    cy.log(`run Tests using test users on: ${system}`);      
    cy.log(JSON.stringify(testData));    
  }); 
  
  cy.fixture(`${system}/suche/testcitizen.json`).then((data) => {      
    cy.log('reading the test data file');      
    testCitizen = data;      
    cy.log(`run Tests using test users on: ${system}`);      
    cy.log(JSON.stringify(testCitizen));    
  }); 
});

it('Prüfung anlegen und nach Lizenz suchen', () => {  
  allure.step('Als Prüfer anmelden', () => {
    allure.parameter("username", testUsers.user2.username);
    allure.parameter("userPassword", '');
    cy.visit('/');
    loginPage.getLoginButton().click();
    externalLoginPage.getLoginPageUsernameInput().type(testUsers.user2.username);
    externalLoginPage.getLoginPagePasswordInput().type(testUsers.user2.password, {log: false});
    externalLoginPage.getLoginPageSubmitButton().click();
});

  allure.step('Validierung des Prüfungsformulars',()=>{
    examinationPage.getExaminationPage().should('be.visible')
    examinationPage.getExamPageTitle().should('be.visible')
    examinationPage.getExamPageIcon().should('be.visible')
    examinationPage.getExamPageDesc().should('be.visible')
    examinationPage.getExamPageForm().should('be.visible')
    examinationPage.getExamPageFormFirstname().should('be.visible').click()
    examinationPage.getExamPageFormLastname().should('be.visible').click()
    examinationPage.getExamPageFormBirthname().should('be.visible').click()
    examinationPage.getExamPageFormBirthdate().should('be.visible').click()
    examinationPage.getExamPageFormBirthplace().should('be.visible').click()
    examinationPage.getExamPageFormPassDate().should('be.visible').click()
    examinationPage.getExamPageFormBirthname().click()
    examinationPage.getExamPageContinueButton().should('not.be.enabled')
    examinationPage.getExamPageFormFirstnameInput().should('have.class', 'ng-invalid');
    examinationPage.getExamPageFormLastnameInput().should('have.class', 'ng-invalid');
    examinationPage.getExamPageFormBirthdateInput().should('have.class', 'ng-invalid');
    examinationPage.getExamPageFormBirthplaceInput().should('have.class', 'ng-invalid');
    examinationPage.getExamPageFormPassDateInput().should('have.class', 'ng-invalid');
  })
  
  allure.step('Befüllen des Prüfungsformulars',()=>{
    examinationPage.getExaminationPage().should('be.visible')
    examinationPage.getExamPageFormFirstname().should('be.visible').type(testCitizen.buerger6.person.firstname)
    examinationPage.getExamPageFormLastname().should('be.visible').type(testCitizen.buerger6.person.lastname)
    examinationPage.getExamPageFormBirthdate().should('be.visible').type(testCitizen.buerger6.person.birthdate)
    examinationPage.getExamPageFormBirthplace().should('be.visible').type(testCitizen.buerger6.person.birthplace)
    examinationPage.getExamPageFormPassDate().should('be.visible').type("2009-01-01")
    examinationPage.getExamPageContinueButton().should('be.visible').click()
  }) 

  allure.step('Überprüfung und Anpassung der Nutzer Daten',()=>{
    examinationPage.getExamConfirmationPage().should('be.visible')
    examinationPage.getExamConfirmationPageIcon().should('be.visible')
    examinationPage.getExamConfirmationPageTitle().should('be.visible')
    examinationPage.getExamConfirmationPageDesc().should('be.visible')
    examinationPage.getExamSummary().should('be.visible').contains("Leo")
    examinationPage.getExamEditButton().should('be.visible').click()
    examinationPage.getExaminationPage().should('be.visible')
    examinationPage.getExamPageFormFirstnameInput().should('be.visible').clear().type("Leonard")
    examinationPage.getExamPageContinueButton().click()
    examinationPage.getExamConfirmationPage().should('be.visible')
    examinationPage.getExamSummary().should('be.visible').contains('Leonard')
    examinationPage.getExamSummary().should('be.visible').contains(testCitizen.buerger6.person.lastname)
    examinationPage.getExamSummary().should('be.visible').contains(testCitizen.buerger6.person.birthdate)
    examinationPage.getExamSummary().should('be.visible').contains(testCitizen.buerger6.person.birthplace)
    examinationPage.getExamSummary().should('be.visible').contains('01.01.2009')
    examinationPage.getExamSubmitButton().should('be.visible').click()
    examinationPage.getSuccessPage().should('be.visible')
  }) 

  allure.step('Überprüfung der "Erfolgreich angelegt" Seite',()=>{
    examinationPage.getSuccessPage().should('be.visible').contains("Erfolgreich angelegt!")
    examinationPage.getSuccessPage().should('be.visible').contains("Bestandene Prüfung wurde erfolgreich im Fischereiregister aufgenommen.")
    examinationPage.getSuccessEntries().invoke('text')
    .then((text) => {
      const match = text.match(/ZF\d{2}-\d{4}-\d{4}-\d{4}/);
      cy.wrap(match[0]).as('accessCode')
    });
    cy.get('@accessCode').then((accessCode) => {
      cy.log(`Extracted Code: ${accessCode}`);
      examinationPage.getSuccessEntries().should('be.visible').contains(accessCode)
    });
  })

  allure.step('Als Prüfer abmelden', () => {    
    digitizeLicencePage.getHeaderComponent().getHeaderUserInfoLogoutButton().eq(0).should('be.visible').click();  
  });

  allure.step('Als Mitarbeiter der oberen Fischereibehörde SH anmelden', () => {
    allure.parameter("username", testUsers.user5.username);
    allure.parameter("userPassword", '');
    cy.visit('/');
    loginPage.getLoginButton().click();
    externalLoginPage.getLoginPageUsernameInput().type(testUsers.user5.username);
    externalLoginPage.getLoginPagePasswordInput().type(testUsers.user5.password, {log: false});
    externalLoginPage.getLoginPageSubmitButton().click();
    homePage.getHomePage().should('be.visible');
  });
  
  allure.step('Suche nach LizenzID durchführen und Serviceübersicht öffnen', () => {
    cy.get('@accessCode').then((accessCode) => {
      searchResultsPage.getSearchBarInputComponent().getSearchbarComponentInput().should('be.visible')
        .type(accessCode);
    });
    homePage.getSearchBarInputComponent().getSearchbarComponentSearchButton().click();
    searchResultsPage.getSearchResultsComponent().getSearchResultsRowInpectionLink().should('be.visible').click()
    serviceOverviewPage.getUserProfileHeader().should('be.visible')
      .contains(`${testCitizen.buerger7.person.firstname} ${testCitizen.buerger7.person.lastname}`)
  })
});

it('Überprüfung aller Bereiche auf die Seite Serviceübersicht', () => {  
  allure.step('Überprüfe den Bereich Fischereiabgabe',()=>{
    serviceOverviewPage.getTaxPaymentCard().should('be.visible')
    serviceOverviewPage.getTaxPaymentButton().should('be.visible').click()
    consentPage.getConsentTaxPaymentPage().should('be.visible')
    headerComponentPage.getServiceOverviewButton().should('be.visible').click()
    serviceOverviewPage.getServiceOverviewPage().should('be.visible')
  })

  allure.step('Überprüfe den Bereich Dokumente',()=>{
    serviceOverviewPage.getDocumentsCard().should('be.visible')
    serviceOverviewPage.getDocumentsButton().should('be.visible')
    serviceOverviewPage.getFishingLicenceCard().should('be.visible')
  })

  allure.step('Überprüfe den Bereich Fischereischein',()=>{
    serviceOverviewPage.getFishingLicenceButton().should('be.visible')
  })
})

it("Zuständigkeit vergeben",()=>{
  allure.step('Öffne das Zuständigkeitsvergabe-Formular.',()=>{
    serviceOverviewPage.getMoveJurisdiction().should('be.visible').click()
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionPage().should('be.visible').contains("Sie sind im Begriff, die Zuständigkeit für Leonard Müller in Ihr Bundesland zu überführen.")
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionPage().should('be.visible').contains("Diese Aktion kann nicht rückgängig gemacht werden.")
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionPage().should('be.visible').contains("Wenden Sie sich im Falle eines Fehlers bitte an eine:n Administrator:in.")
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionPage().should('be.visible').contains("Zukünftige Zuständigkeit für Leonard Müller liegt in:")
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionPage().should('be.visible').contains("Schleswig-Holstein")
  })

  allure.step('Fülle das Zuständigkeitsvergabe-Formular aus.',()=>{
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionForm().should('be.visible')
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveCertificate().should('be.visible')
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveCertificateThirdParty().should('be.visible')
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveCertificateCheckbox().should('be.visible').click()
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveCertificateThirdPartyCheckbox().should('be.visible')
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveJursidictionContinueButton().should('be.visible').click()
  })

  allure.step('Akzeptiere DSVGO',()=>{
    consentPage.getConsentDigitizePage().should('be.visible')
    consentPage.getConsentForm().should('be.visible')
    consentPage.getInfoLink().should('be.visible')
    consentPage.getPdfLink().should('be.visible')
    consentPage.getConsentFormGDPR().should('be.visible')
    consentPage.getConsentFormSelfDisclosure().should('be.visible')
    consentPage.getSelfDisclosureCheckbox().should('be.visible').click()
    consentPage.getGdprCheckbox().should('be.visible').click()
    consentPage.getConsentPageContinueButton().should('be.visible').click()
  })

  allure.step('Fülle das Fischereiabgabeformular aus',()=>{
    digitizeLicencePage.getTaxesComponent().getTaxesBoxHeader().should('be.visible').contains("0,00 €")
    digitizeLicencePage.getTaxesComponent().getTaxesExtraLicence().should('be.visible').contains("— €")
    digitizeLicencePage.getTaxesComponent().getTaxesExtraLicenceCheckbox().should('be.visible')
    digitizeLicencePage.getTaxesComponent().getPaymentMethodCard().should('be.visible')
    digitizeLicencePage.getTaxesComponent().getPaymentMethodCash().should('be.visible')
    headerComponentPage.getAllTabTitleState().eq(0).should('have.class', 'active')
    headerComponentPage.getAllTabTitleState().eq(0).should('not.be.disabled')
    headerComponentPage.getAllTabTitleState().eq(1).should('have.class', 'inactive')
    headerComponentPage.getAllTabTitleState().eq(1).should('be.disabled')
    digitizeLicencePage.getEditFooterComponent().getFooterFinishButton().should('be.visible').click()
  })

  allure.step('Überprüfe die Zuständigkeit-Umzug Seite',()=>{
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveJurisdictionConfirmationPage().should('be.visible').contains("Zuständigkeits-Umzug erfolgreich!")
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveJurisdictionConfirmationHeader().should('be.visible').contains("Anpassung wurde wurde erfolgreich im Fischereiregister aufgenommen.")
    serviceOverviewPage.getMoveJurisdictionComponent().getMoveJurisdictionConfirmationTableEntries().should('be.visible').contains("SH")
    headerComponentPage.getAllTabTitleState().eq(0).should('have.class', 'inactive')
    headerComponentPage.getAllTabTitleState().eq(0).should('be.disabled')
    headerComponentPage.getAllTabTitleState().eq(1).should('have.class', 'active')
    headerComponentPage.getAllTabTitleState().eq(1).should('not.be.disabled')
  })
})

it("Sperre anlegen und Registereintrag befristet sperren",()=>{
  const banType = "temporaryBan"
  const formattedTomorrow = tomorrowDate.toISOString().split('T')[0];

  allure.step('Wähle "Person sperren" auf die Serviceübersicht Seite',()=>{
    headerComponentPage.getServiceOverviewButton().should('be.visible').click()
    serviceOverviewPage.getUserProfileHeader().should('be.visible')
      .contains(`${testCitizen.buerger7.person.firstname} ${testCitizen.buerger7.person.lastname}`)
    editFooterComponentPage.getFooterDeleteButton().should('be.visible')
    editFooterComponentPage.getFooterBanButton().should('be.visible').click()
  })

  allure.step('Überprüfe das Formular "Begründung"',()=>{
    banPersonPage.getBanUserPage().should('be.visible')
    banPersonPage.getBanReasonTab().should('be.visible')
    banPersonPage.getBanReporter().should('be.visible').click()
    banPersonPage.getBanFile().should('be.visible').click()
    editFooterComponentPage.getFooterContinueButton().should('be.visible')
    headerComponentPage.getAllTabTitleState().eq(0).should('have.class', 'active')
    headerComponentPage.getAllTabTitleState().eq(0).should('not.be.disabled')
    headerComponentPage.getAllTabTitleState().eq(1).should('have.class', 'inactive')
    headerComponentPage.getAllTabTitleState().eq(1).should('be.disabled')
    headerComponentPage.getAllTabTitleState().eq(2).should('have.class', 'inactive')
    headerComponentPage.getAllTabTitleState().eq(2).should('be.disabled')
  })

  allure.step('Fülle das Formular "Begründung" aus',()=>{
    banPersonPage.getBanReporterInput().type(testUsers.user5.given_name)
    banPersonPage.getBanFileInput().type(banType)
    editFooterComponentPage.getFooterContinueButton().should('be.visible').click()
  })

  allure.step('Überprüfe das Formular "Zeitraum"',()=>{
    banPersonPage.getBanPeriodTab().should('be.visible')
    banPersonPage.getBanTemporaryOption().should('be.visible')
    banPersonPage.getTemporaryStartDate().should('be.visible').click()
    banPersonPage.getTemporaryEndDate().should('be.visible').click()
    editFooterComponentPage.getFooterFinishButton().should('be.visible')
    banPersonPage.getBanPermanentOption().should('be.visible').click()
    banPersonPage.getPermanentStartDate().should('be.visible').click()
    editFooterComponentPage.getFooterFinishButton().should('be.visible')
    headerComponentPage.getAllTabTitleState().eq(0).should('have.class', 'inactive')
    headerComponentPage.getAllTabTitleState().eq(0).should('not.be.disabled')
    headerComponentPage.getAllTabTitleState().eq(1).should('have.class', 'active')
    headerComponentPage.getAllTabTitleState().eq(1).should('not.be.disabled')
    headerComponentPage.getAllTabTitleState().eq(2).should('have.class', 'inactive')
    headerComponentPage.getAllTabTitleState().eq(2).should('be.disabled')
  })

  allure.step('Fülle das Formular "Zeitraum" aus',()=>{
    if(Cypress.env('envName') === 'dev'){
      banPersonPage.getRadios("temporary").eq(0).click({force:true})
    }
    else if(Cypress.env('envName') === 'test'){
      banPersonPage.getRadios("temporary").click({force:true})
    }
    banPersonPage.getTemporaryStartDateInput().type(currentDate)
    banPersonPage.getTemporaryEndDateInput().type(formattedTomorrow)
    editFooterComponentPage.getFooterFinishButton().should('be.visible').click()
  })

  allure.step('Überprüfe die Seite Sperrung erfolgreich',()=>{
    banPersonPage.getBanUserPage().should('be.visible').contains("Sperrung erfolgreich!")
    banPersonPage.getBanUserPage().contains("Eintrag wurde erfolgreich im Fischereiregister aufgenommen.")
    banPersonPage.getBanUserPage().contains("Eintrag wurde erfolgreich im Fischereiregister aufgenommen.")
    if(Cypress.env('envName') === 'dev'){
      banPersonPage.getBannedCitizenName().should('be.visible').contains(`${testCitizen.buerger7.person.firstname} ${testCitizen.buerger7.person.lastname}`)
      cy.changeDateFormat(currentDate).then((date)=>{
        banPersonPage.getBannedStartDate().should('be.visible').contains(date)
      })
      cy.changeDateFormat(formattedTomorrow).then((date)=>{
        banPersonPage.getBannedEndDate().should('be.visible').contains(date)
      })
    banPersonPage.getBannedFile().should('be.visible').contains(banType)
    }
    else if(Cypress.env('envName') === 'test'){
      banPersonPage.getBanUserPage().contains(`${testCitizen.buerger7.person.firstname} ${testCitizen.buerger7.person.lastname}`)
      cy.changeDateFormat(currentDate).then((date)=>{
        banPersonPage.getBanUserPage().contains(date)
      })
      cy.changeDateFormat(formattedTomorrow).then((date)=>{
        banPersonPage.getBanUserPage().should('be.visible').contains(date)
      })
      banPersonPage.getBanUserPage().contains(banType)
    }
    headerComponentPage.getAllTabTitleState().eq(0).should('have.class', 'inactive')
    headerComponentPage.getAllTabTitleState().eq(0).should('be.disabled')
    headerComponentPage.getAllTabTitleState().eq(1).should('have.class', 'inactive')
    headerComponentPage.getAllTabTitleState().eq(1).should('be.disabled')
    headerComponentPage.getAllTabTitleState().eq(2).should('have.class', 'active')
    headerComponentPage.getAllTabTitleState().eq(2).should('not.be.disabled')
    headerComponentPage.getServiceOverviewButton().should('be.visible').click()
  })

  allure.step('Überprüfe der Sperre auf die Serviceübersicht Seite',()=>{
    serviceOverviewPage.getServiceOverviewPage().should('be.visible')
    serviceOverviewPage.getBanHeader().should('be.visible').contains("Gesperrt")
    serviceOverviewPage.getBanDescription().invoke('text').then((text)=>{
      cy.temporaryBanDate(text).should((data) => {
        expect(data.startDate).to.eq(currentDate);
        expect(data.endDate).to.eq(formattedTomorrow);
        expect(data.type).to.eq(banType);
        expect(data.name).to.eq(testUsers.user5.given_name);
      });
    })
    serviceOverviewPage.getBanEditButton().should('be.visible')
    serviceOverviewPage.getUnbanButton().should('be.visible')
  })
})

it("Sperre bearbeiten und Registereintrag lebenslang sperren",()=>{
  const banType = "permanentBan"
  const banEnd = "Lebenslang"

  allure.step('Bearbeite eine Sperre',()=>{
    serviceOverviewPage.getBanEditButton().click()
  })

  allure.step('Fülle das Formular "Begründung" aus',()=>{
    banPersonPage.getBanUserPage().should('be.visible')
    banPersonPage.getBanReporterInput().clear().type(testUsers.user5.given_name)
    banPersonPage.getBanFileInput().clear().type(banType)
    editFooterComponentPage.getFooterContinueButton().should('be.visible').click()
  })

  allure.step('Fülle das Formular "Zeitraum" aus',()=>{
    if(Cypress.env('envName') === 'dev'){
      banPersonPage.getRadios("permanent").eq(1).click({force:true})
    }
    else if(Cypress.env('envName') === 'test'){
      banPersonPage.getRadios("permanent").click({force:true})
    }
    banPersonPage.getPermanentStartDateInput().type(currentDate)
    editFooterComponentPage.getFooterFinishButton().should('be.visible').click()
  })

  allure.step('Überprüfe die Seite Sperrung erfolgreich',()=>{
    banPersonPage.getBanUserPage().should('be.visible').contains("Sperrung erfolgreich!")
    banPersonPage.getBanUserPage().contains("Eintrag wurde erfolgreich im Fischereiregister aufgenommen.")
    if(Cypress.env('envName') === 'dev'){
      banPersonPage.getBannedCitizenName().should('be.visible').contains(`${testCitizen.buerger7.person.firstname} ${testCitizen.buerger7.person.lastname}`)
      cy.changeDateFormat(currentDate).then((date)=>{
        banPersonPage.getBannedStartDate().should('be.visible').contains(date)
      })
      banPersonPage.getBannedEndDate().should('be.visible').contains("—")
      banPersonPage.getBannedFile().should('be.visible').contains(banType)
    }
    else if(Cypress.env('envName') === 'test'){
      banPersonPage.getBanUserPage().contains(`${testCitizen.buerger7.person.firstname} ${testCitizen.buerger7.person.lastname}`)
      cy.changeDateFormat(currentDate).then((date)=>{
        banPersonPage.getBanUserPage().contains(date)
      })
      banPersonPage.getBanUserPage().contains("—")
      banPersonPage.getBanUserPage().contains(banType)
    }
    headerComponentPage.getServiceOverviewButton().should('be.visible').click()
  })

  allure.step('Überprüfe die Sperre auf die Seite Serviceübersicht',()=>{
    serviceOverviewPage.getServiceOverviewPage().should('be.visible')
    serviceOverviewPage.getBanHeader().should('be.visible').contains("Gesperrt")
    serviceOverviewPage.getBanDescription().invoke('text').then((text)=>{
    cy.permanentBanDate(text,banEnd).should((data) => {
      expect(data.startDate).to.eq(currentDate);
      expect(data.endDate).to.eq(banEnd);
      expect(data.type).to.eq(banType);
      expect(data.name).to.eq(testUsers.user5.given_name);
    });
  })  
    serviceOverviewPage.getBanEditButton().should('be.visible')
    serviceOverviewPage.getUnbanButton().should('be.visible')
  })
})

it("Registereintrag entsperren",()=>{
  allure.step('Führe eine Entsperrung durch',()=>{
    serviceOverviewPage.getUnbanButton().click()
    serviceOverviewPage.getUnbanDialog().should('be.visible')
    serviceOverviewPage.getUnbanConfirmationbutton().click()
    serviceOverviewPage.getServiceOverviewPage().should('be.visible')
    serviceOverviewPage.getBanHeader().should('not.exist')
    editFooterComponentPage.getFooterBanButton().click()
    banPersonPage.getBanReasonTab().should('be.visible')
  })
})

it("Registereintrag löschen",()=>{
  allure.step('Lösche einen Registereintrag',()=>{
    headerComponentPage.getServiceOverviewButton().should('be.visible').click()
    editFooterComponentPage.getFooterDeleteButton().should('be.visible')
  })

  allure.step('Als Mitarbeiter der oberen Fischereibehörde SH abmelden.', () => {    
    digitizeLicencePage.getHeaderComponent().getHeaderUserInfoLogoutButton().eq(0).should('be.visible').click();  
  });
})

})
