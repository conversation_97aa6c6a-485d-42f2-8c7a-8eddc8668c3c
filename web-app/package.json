{"name": "web-app", "version": "0.18.0-snapshot", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "storybook": "ng run web-app:storybook", "build-storybook": "ng run web-app:build-storybook", "e2e": "ng e2e", "cypress:run:test": "cypress run --env envName=test allure=true", "cypress:run:dev": "cypress run --env envName=dev allure=true", "lint": "ng lint", "lint-fix": "ng lint --fix=true", "analyze-webpack": "webpack-bundle-analyzer ./dist/web-app/stats.json", "prepare": "cd .. && (husky web-app/.husky || true)", "sbom": "npm sbom --sbom-format cyclonedx --sbom-type application > application.cdx.json"}, "private": true, "dependencies": {"@angular/animations": "^17.3.0", "@angular/cdk": "^17.3.10", "@angular/common": "^17.3.0", "@angular/compiler": "^17.3.0", "@angular/core": "^17.3.0", "@angular/forms": "^17.3.0", "@angular/platform-browser": "^17.3.0", "@angular/platform-browser-dynamic": "^17.3.0", "@angular/router": "^17.3.0", "@digifischdok/ngx-register-sdk": "1.11.2", "@ngrx/signals": "^17.2.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.0", "keycloak-angular": "^15.2.1", "keycloak-js": "^24.0.5", "ng2-charts": "^8.0.0", "rxjs": "~7.8.0", "tailwind-merge": "^2.3.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.6", "@angular-eslint/builder": "17.3.0", "@angular-eslint/eslint-plugin": "17.3.0", "@angular-eslint/eslint-plugin-template": "17.3.0", "@angular-eslint/schematics": "17.3.0", "@angular-eslint/template-parser": "17.3.0", "@angular/cli": "^17.3.6", "@angular/compiler-cli": "^17.3.0", "@chromatic-com/storybook": "^3.2.2", "@compodoc/compodoc": "^1.1.16", "@cypress/eslint-plugin-dev": "^6.0.0", "@cypress/schematic": "^2.5.1", "@ngrx/eslint-plugin": "^17.2.0", "@shelex/cypress-allure-plugin": "^2.40.2", "@storybook/addon-a11y": "^8.6.12", "@storybook/addon-docs": "^8.6.12", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/angular": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/manager-api": "^8.6.12", "@storybook/test": "^8.6.12", "@storybook/theming": "^8.6.12", "@stylistic/eslint-plugin": "^2.10.1", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/chai": "^5.0.1", "@types/cypress": "^0.1.6", "@types/jasmine": "~5.1.0", "@types/mocha": "^10.0.9", "@types/node": "^22.8.1", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "allure-commandline": "^2.30.0", "allure-cypress": "^3.2.2", "allure-js-commons": "^3.0.4", "autoprefixer": "^10.4.19", "axe-core": "^4.10.0", "chai": "^5.1.2", "chai-jquery": "^2.1.0", "cypress": "^13.15.0", "cypress-axe": "^1.5.0", "cypress-multi-reporters": "^2.0.5", "eslint": "^8.57.1", "eslint-config-next": "^15.0.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "^4.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-json-format": "^2.0.1", "eslint-plugin-mocha": "^10.5.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-rxjs": "^5.0.3", "husky": "^9.1.6", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "lint-staged": "^15.2.10", "mocha": "^11.3.0", "mochawesome": "^7.1.3", "mochawesome-report-generator": "^6.2.0", "node.os": "^1.2.4", "postcss": "^8.4.38", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "storybook": "^8.6.12", "storybook-addon-angular-router": "^1.10.0", "tailwindcss": "^3.4.3", "typescript": "~5.4.2", "typescript-eslint": "^8.8.0", "webpack-bundle-analyzer": "^4.10.2"}}