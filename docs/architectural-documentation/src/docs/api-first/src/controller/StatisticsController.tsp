import "@typespec/http";
import "@typespec/openapi3";

import "../common.tsp";
import "../Fischereiregister.tsp";
import "../model/enum/FederalStateAbbreviation.tsp";
import "../model/statistics/BansStatistics.tsp";
import "../model/statistics/CertificationsStatistics.tsp";
import "../model/statistics/ErrorsStatistics.tsp";
import "../model/statistics/InspectionsStatistics.tsp";
import "../model/statistics/LicensesStatistics.tsp";
import "../model/statistics/TaxesStatistics.tsp";

using TypeSpec.Http;

namespace Fischereiregister;

@tag("Statistics")
@route("/statistics")
interface StatisticsController { 

    @get
    @route("/taxes")
    @doc("Retrieves taxation statistics for the specified years, optionally filtered by office or federal state.")
    getTaxesStatistics( 
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The office name for which the statistics should be retrieved. If specified, data will be filtered accordingly.")
        @query office?: string,

        @doc("The federal state for which the statistics should be retrieved. This is used only if no office is specified. If no Federal State Or Office is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): WithStandardErrors<TaxesStatistics[]>;    

    @get
    @route("/licenses/regular")
    @doc("Retrieves statistics for regular fishing licenses issued within the specified years, optionally filtered by office or federal state.")
    getRegularLicensesStatistics( 
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The office name for which the statistics should be retrieved. If specified, data will be filtered accordingly.")
        @query office?: string,

        @doc("The federal state for which the statistics should be retrieved. This is used only if no office is specified. If no Federal State Or Office is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): WithStandardErrors<LicensesStatistics[]>;    

    @get
    @route("/licenses/vacation")
    @doc("Retrieves statistics for vacation fishing licenses issued within the specified years, optionally filtered by office or federal state.")
    getVacationLicensesStatistics( 
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The office name for which the statistics should be retrieved. If specified, data will be filtered accordingly.")
        @query office?: string,

        @doc("The federal state for which the statistics should be retrieved. This is used only if no office is specified. If no Federal State Or Office is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): WithStandardErrors<LicensesStatistics[]>;
    
    @get
    @route("/licenses/limited")
    @doc("Retrieves statistics for limited access fishing licenses issued within the specified years, optionally filtered by office or federal state.")
    getLimitedLicensesStatistics( 
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The office name for which the statistics should be retrieved. If specified, data will be filtered accordingly.")
        @query office?: string,

        @doc("The federal state for which the statistics should be retrieved. This is used only if no office is specified. If no Federal State Or Office is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): WithStandardErrors<LicensesStatistics[]>;
    
    @get
    @route("/bans")
    @doc("Retrieves statistics for issued and expired bans within the specified years, optionally filtered by federal state.")
    getBansStatistics( 
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The federal state for which the statistics should be retrieved. If no Federal State is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): WithStandardErrors<BansStatistics[]>;    
    
    
    @get
    @route("/certifications")
    @doc("[DRAFT] Retrieves statistics for certifications issued within the specified years, optionally filtered by office or federal state.")
    #deprecated "THIS IS AN API DRAFT AND IS NOT READY YET" 
    getCertificationsStatistics( 
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The office name for which the statistics should be retrieved. If specified, data will be filtered accordingly.")
        @query office?: string,

        @doc("The federal state for which the statistics should be retrieved. This is used only if no office is specified. If no Federal State Or Office is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): WithStandardErrors<CertificationsStatistics[]>;    
    
    
    @get
    @route("/inspections")
    @doc("[DRAFT] Retrieves statistics for fishing inspections conducted within the specified years, filtered by federal state.")
    #deprecated "THIS IS AN API DRAFT AND IS NOT READY YET" 
    getInspectionsStatistics( 
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The federal state for which the statistics should be retrieved. If no Federal State is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): WithStandardErrors<InspectionsStatistics[]>;
    
     
    @get
    @route("/errors")
    @doc("[DRAFT] Retrieves statistics on request processing errors within the specified years, filtered by federal state.")
    #deprecated "THIS IS AN API DRAFT AND IS NOT READY YET" 
    getErrorsStatistics( 
        @doc("List of years for which the statistics are requested. If no year is provided, all years found in system are returned.")
        @query({
            format: "multi",
            name: "year",
        }) year?: int32[],

        @doc("The federal state for which the statistics should be retrieved. If no Federal State is given, an Overall general Statistic is to be delivered")
        @query federalState?: FederalStateAbbreviation, 
        
    ): WithStandardErrors<ErrorsStatistics[]>;
}
