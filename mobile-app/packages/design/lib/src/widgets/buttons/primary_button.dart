import 'package:flutter/material.dart';

import '../../../design.dart';

class PrimaryButton extends StatelessWidget {
  const PrimaryButton({
    super.key,
    required this.onPressed,
    required this.title,
    required this.semanticText,
    this.alignment,
    this.isBlueShadow = true,
    this.color,
    this.textColor,
    this.leadingIcon,
  });

  final VoidCallback? onPressed;
  final String title;
  final MainAxisAlignment? alignment;
  final Color? color;
  final Color? textColor;
  final bool isBlueShadow;
  final Widget? leadingIcon;
  final String? semanticText;
  @override
  Widget build(BuildContext context) {
    final buttonTitle = FittedBox(
      fit: BoxFit.scaleDown,
      child: Text(
        title,
        maxLines: 1,
        style: textColor != null
            ? Theme.of(context).formLabel.copyWith(color: textColor)
            : Theme.of(context).formLabel.copyWith(
                  color: Colors.white,
                ),
      ),
    );

    return Semantics(
      label: semanticText,
      button: true,
      child: GestureDetector(
        onTap: onPressed,
        child: ConstrainedBox(
          constraints: kDefaultPrimaryButtonConstraints,
          child: Container(
            decoration: BoxDecoration(
              color: color ?? context.theme.primary,
              borderRadius: borderRadiusAll4,
              border: !isBlueShadow
                  ? Border.all(color: context.theme.borderColor)
                  : null,
              boxShadow:
                  isBlueShadow ? blueShadow(context) : whiteShadow(context),
            ),
            child: leadingIcon != null
                ? Row(
                    mainAxisAlignment: alignment ?? MainAxisAlignment.start,
                    children: [
                      spacingHorizontal8,
                      Semantics(
                        excludeSemantics: true,
                        child: leadingIcon!,
                      ),
                      spacingHorizontal8,
                      Flexible(
                        child: buttonTitle,
                      ),
                      alignment == MainAxisAlignment.center
                          ? spacingHorizontal32
                          : SizedBox(),
                    ],
                  )
                : buttonTitle,
          ),
        ),
      ),
    );
  }

  List<BoxShadow> blueShadow(BuildContext context) {
    return [
      BoxShadow(
        color: Color(0x3D163BBF),
        blurRadius: 3,
        offset: Offset(0, 1),
        spreadRadius: 0,
      ),
      BoxShadow(
        color: Color(0x33163BBF),
        blurRadius: 5,
        offset: Offset(0, 5),
        spreadRadius: 0,
      ),
      BoxShadow(
        color: Color(0x1E163BBF),
        blurRadius: 7,
        offset: Offset(0, 12),
        spreadRadius: 0,
      ),
      BoxShadow(
        color: Color(0x0A163BBF),
        blurRadius: 9,
        offset: Offset(0, 21),
        spreadRadius: 0,
      ),
      BoxShadow(
        color: Color(0x00163BBF),
        blurRadius: 9,
        offset: Offset(0, 33),
        spreadRadius: 0,
      ),
    ];
  }

  List<BoxShadow> whiteShadow(BuildContext context) {
    return [
      BoxShadow(
        color: context.theme.background.withOpacity(0.14),
        blurRadius: 3,
        offset: Offset(0, 1),
        spreadRadius: 0,
      ),
      BoxShadow(
        color: context.theme.background.withOpacity(0.16),
        blurRadius: 5,
        offset: Offset(0, 5),
        spreadRadius: 0,
      ),
    ];
  }
}
